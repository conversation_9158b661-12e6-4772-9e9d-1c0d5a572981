import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

const prisma = new PrismaClient();

// Validation schemas
const updateInventorySchema = z.object({
  quantity: z.number().min(0),
  lowStockThreshold: z.number().min(0).optional(),
  allowBackorder: z.boolean().optional(),
  notes: z.string().optional()
});

const inventoryFiltersSchema = z.object({
  status: z.enum(['all', 'in_stock', 'low_stock', 'out_of_stock']).optional(),
  category: z.string().optional(),
  brand: z.string().optional(),
  search: z.string().optional(),
  page: z.string().optional(),
  limit: z.string().optional(),
  sortBy: z.enum(['name', 'stock', 'value', 'lastUpdated']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});

class InventoryController {
  // Get inventory data with filtering and pagination
  static async getInventory(req: Request, res: Response): Promise<void> {
    try {
      const validation = inventoryFiltersSchema.safeParse(req.query);
      if (!validation.success) {
        res.status(400).json({
          success: false,
          message: 'پارامترهای نامعتبر',
          errors: validation.error.errors
        });
        return;
      }

      const {
        status = 'all',
        category,
        brand,
        search,
        page = '1',
        limit = '20',
        sortBy = 'name',
        sortOrder = 'asc'
      } = validation.data;

      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const offset = (pageNum - 1) * limitNum;

      // Build where clause
      const where: any = {
        product: {
          isActive: true
        }
      };

      // Filter by stock status
      if (status !== 'all') {
        switch (status) {
          case 'out_of_stock':
            where.quantity = 0;
            break;
          case 'low_stock':
            where.quantity = {
              gt: 0,
              lte: prisma.$queryRaw`inventory.low_stock_threshold`
            };
            break;
          case 'in_stock':
            where.quantity = {
              gt: prisma.$queryRaw`inventory.low_stock_threshold`
            };
            break;
        }
      }

      // Filter by category
      if (category) {
        where.product.categories = {
          some: {
            category: {
              slug: category
            }
          }
        };
      }

      // Filter by brand
      if (brand) {
        where.product.brand = {
          slug: brand
        };
      }

      // Search filter
      if (search) {
        where.OR = [
          {
            product: {
              name: {
                contains: search,
                mode: 'insensitive'
              }
            }
          },
          {
            product: {
              sku: {
                contains: search,
                mode: 'insensitive'
              }
            }
          }
        ];
      }

      // Build order by clause
      const orderBy: any = {};
      switch (sortBy) {
        case 'name':
          orderBy.product = { name: sortOrder };
          break;
        case 'stock':
          orderBy.quantity = sortOrder;
          break;
        case 'lastUpdated':
          orderBy.updatedAt = sortOrder;
          break;
        default:
          orderBy.product = { name: 'asc' };
      }

      // Get inventory data
      const [inventory, total] = await Promise.all([
        prisma.productInventory.findMany({
          where,
          include: {
            product: {
              include: {
                brand: true,
                categories: {
                  include: {
                    category: true
                  }
                },
                images: {
                  where: { isPrimary: true },
                  take: 1
                }
              }
            }
          },
          orderBy,
          skip: offset,
          take: limitNum
        }),
        prisma.productInventory.count({ where })
      ]);

      // Transform data for frontend
      const transformedInventory = inventory.map(item => {
        const product = item.product;
        if (!product) return null;
        const primaryCategory = product.categories?.[0]?.category;
        const primaryImage = product.images?.[0];
        
        // Calculate stock status
        let stockStatus = 'in_stock';
        if (item.quantity === 0) {
          stockStatus = 'out_of_stock';
        } else if (item.quantity <= item.lowStockThreshold) {
          stockStatus = 'low_stock';
        }

        // Calculate total value
        const totalValue = item.quantity * Number(product.price);

        return {
          id: item.id,
          productId: product.id,
          name: product.name,
          sku: product.sku,
          category: primaryCategory?.name || 'عمومی',
          brand: product.brand?.name || '',
          currentStock: item.quantity,
          reservedStock: item.reservedQuantity || 0,
          availableStock: item.quantity - (item.reservedQuantity || 0),
          lowStockThreshold: item.lowStockThreshold,
          price: Number(product.price),
          comparePrice: product.comparePrice ? Number(product.comparePrice) : undefined,
          totalValue,
          lastRestocked: item.updatedAt,
          status: stockStatus,
          allowBackorder: item.allowBackorder,
          trackQuantity: item.trackQuantity,
          image: primaryImage?.url,
          productSlug: product.slug
        };
      }).filter(Boolean);

      // Calculate summary statistics
      const summary = {
        totalItems: total,
        inStock: inventory.filter(item => item.quantity > item.lowStockThreshold).length,
        lowStock: inventory.filter(item => item.quantity > 0 && item.quantity <= item.lowStockThreshold).length,
        outOfStock: inventory.filter(item => item.quantity === 0).length,
        totalValue: inventory.reduce((sum, item) => {
          if (!item.product) return sum;
          return sum + (item.quantity * Number(item.product.price));
        }, 0)
      };

      res.json({
        success: true,
        message: 'اطلاعات موجودی با موفقیت دریافت شد',
        data: {
          inventory: transformedInventory,
          pagination: {
            page: pageNum,
            limit: limitNum,
            total,
            pages: Math.ceil(total / limitNum)
          },
          summary
        }
      });

    } catch (error) {
      console.error('Get inventory error:', error);
      res.status(500).json({
        success: false,
        message: 'خطا در دریافت اطلاعات موجودی',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }

  // Update inventory for a specific product
  static async updateInventory(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const validation = updateInventorySchema.safeParse(req.body);

      if (!validation.success) {
        res.status(400).json({
          success: false,
          message: 'داده‌های نامعتبر',
          errors: validation.error.errors
        });
        return;
      }

      const { quantity, lowStockThreshold, allowBackorder, notes } = validation.data;

      // Check if inventory exists
      const existingInventory = await prisma.productInventory.findUnique({
        where: { id },
        include: { product: true }
      });

      if (!existingInventory) {
        res.status(404).json({
          success: false,
          message: 'موجودی یافت نشد'
        });
        return;
      }

      // Update inventory
      const updatedInventory = await prisma.productInventory.update({
        where: { id },
        data: {
          quantity,
          lowStockThreshold: lowStockThreshold ?? existingInventory.lowStockThreshold,
          allowBackorder: allowBackorder ?? existingInventory.allowBackorder,
          updatedAt: new Date()
        },
        include: {
          product: {
            include: {
              brand: true,
              categories: {
                include: {
                  category: true
                }
              }
            }
          }
        }
      });

      // Create inventory history record
      await prisma.inventoryHistory.create({
        data: {
          inventoryId: id,
          action: 'MANUAL_UPDATE',
          quantityBefore: existingInventory.quantity,
          quantityAfter: quantity,
          quantityChange: quantity - existingInventory.quantity,
          notes: notes || 'به‌روزرسانی دستی موجودی',
          createdBy: req.user?.id || 'system'
        }
      });

      res.json({
        success: true,
        message: 'موجودی با موفقیت به‌روزرسانی شد',
        data: {
          inventory: updatedInventory
        }
      });

    } catch (error) {
      console.error('Update inventory error:', error);
      res.status(500).json({
        success: false,
        message: 'خطا در به‌روزرسانی موجودی',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }

  // Get low stock items
  static async getLowStockItems(req: Request, res: Response): Promise<void> {
    try {
      // Get all inventory items and filter manually for simplicity
      const allItems = await prisma.productInventory.findMany({
        where: {
          product: {
            isActive: true
          }
        },
        include: {
          product: {
            include: {
              brand: true,
              categories: {
                include: {
                  category: true
                }
              }
            }
          }
        },
        orderBy: {
          quantity: 'asc'
        }
      });

      // Filter for low stock items manually
      const lowStockItems = allItems.filter(item =>
        item.quantity === 0 || item.quantity <= item.lowStockThreshold
      );

      const transformedItems = lowStockItems.map(item => {
        if (!item.product) return null;
        return {
          id: item.id,
          productId: item.product.id,
          name: item.product.name,
          sku: item.product.sku,
          category: item.product.categories?.[0]?.category?.name || 'عمومی',
          brand: item.product.brand?.name || '',
          currentStock: item.quantity,
          lowStockThreshold: item.lowStockThreshold,
          status: item.quantity === 0 ? 'out_of_stock' : 'low_stock',
          urgency: item.quantity === 0 ? 'critical' : 'warning'
        };
      }).filter(Boolean);

      res.json({
        success: true,
        message: 'اقلام کم موجود با موفقیت دریافت شد',
        data: {
          items: transformedItems,
          count: transformedItems.length
        }
      });

    } catch (error) {
      console.error('Get low stock items error:', error);
      res.status(500).json({
        success: false,
        message: 'خطا در دریافت اقلام کم موجود',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }

  // Get inventory statistics
  static async getInventoryStats(req: Request, res: Response): Promise<void> {
    try {
      // Get all inventory items and calculate stats manually
      const allInventory = await prisma.productInventory.findMany({
        where: {
          product: { isActive: true }
        },
        include: {
          product: true
        }
      });

      const totalProducts = allInventory.length;
      let inStockCount = 0;
      let lowStockCount = 0;
      let outOfStockCount = 0;
      let totalQuantity = 0;

      allInventory.forEach(item => {
        totalQuantity += item.quantity;

        if (item.quantity === 0) {
          outOfStockCount++;
        } else if (item.quantity <= item.lowStockThreshold) {
          lowStockCount++;
        } else {
          inStockCount++;
        }
      });

      const stats = {
        totalProducts,
        inStock: inStockCount,
        lowStock: lowStockCount,
        outOfStock: outOfStockCount,
        totalQuantity,
        stockHealth: {
          healthy: totalProducts > 0 ? Math.round((inStockCount / totalProducts) * 100) : 0,
          warning: totalProducts > 0 ? Math.round((lowStockCount / totalProducts) * 100) : 0,
          critical: totalProducts > 0 ? Math.round((outOfStockCount / totalProducts) * 100) : 0
        }
      };

      res.json({
        success: true,
        message: 'آمار موجودی با موفقیت دریافت شد',
        data: stats
      });

    } catch (error) {
      console.error('Get inventory stats error:', error);
      res.status(500).json({
        success: false,
        message: 'خطا در دریافت آمار موجودی',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      });
    }
  }
}

export default InventoryController;
