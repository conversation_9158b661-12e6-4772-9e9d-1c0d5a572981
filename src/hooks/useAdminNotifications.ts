import { useState, useEffect, useCallback } from 'react';
import {
  AdminNotification,
  NotificationFilters,
  NotificationSettings,
  AuditLogEntry,
  AuditLogFilters,
  NotificationType,
  NotificationCategory
} from '../types/adminNotifications';
import { API_BASE_URL } from '../config/api';
import toast from 'react-hot-toast';

// API service for admin notifications
class AdminNotificationApiService {
  private static baseUrl = `${API_BASE_URL}/api/v1`;

  static async getNotifications(filters?: NotificationFilters): Promise<AdminNotification[]> {
    try {
      const params = new URLSearchParams();
      if (filters?.category) params.append('category', filters.category.join(','));
      if (filters?.type) params.append('type', filters.type.join(','));
      if (filters?.priority) params.append('priority', filters.priority.join(','));
      if (filters?.read !== undefined) params.append('read', filters.read.toString());

      const token = localStorage.getItem('authToken') || localStorage.getItem('adminToken');

      const response = await fetch(`${this.baseUrl}/admin/notifications?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success && data.data?.notifications) {
        return data.data.notifications.map(this.transformBackendNotification);
      }

      return [];
    } catch (error) {
      console.warn('Failed to load notifications from API, using fallback:', error);
      return this.getFallbackNotifications();
    }
  }

  static async markAsRead(notificationId: string): Promise<void> {
    try {
      const token = localStorage.getItem('authToken') || localStorage.getItem('adminToken');

      const response = await fetch(`${this.baseUrl}/admin/notifications/${notificationId}/read`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('خطا در علامت‌گذاری به عنوان خوانده شده');
      }
    } catch (error) {
      console.error('Mark as read error:', error);
      throw error;
    }
  }

  static async markAllAsRead(): Promise<void> {
    try {
      const token = localStorage.getItem('authToken') || localStorage.getItem('adminToken');

      const response = await fetch(`${this.baseUrl}/admin/notifications/mark-all-read`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('خطا در علامت‌گذاری همه اعلان‌ها');
      }
    } catch (error) {
      console.error('Mark all as read error:', error);
      throw error;
    }
  }

  static async deleteNotification(notificationId: string): Promise<void> {
    try {
      const token = localStorage.getItem('authToken') || localStorage.getItem('adminToken');

      const response = await fetch(`${this.baseUrl}/admin/notifications/${notificationId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('خطا در حذف اعلان');
      }
    } catch (error) {
      console.error('Delete notification error:', error);
      throw error;
    }
  }

  static async createNotification(
    type: NotificationType,
    title: string,
    message: string,
    data?: any
  ): Promise<AdminNotification> {
    try {
      const token = localStorage.getItem('authToken') || localStorage.getItem('adminToken');

      const response = await fetch(`${this.baseUrl}/admin/notifications`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          type,
          title,
          message,
          data
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'خطا در ایجاد اعلان');
      }

      const responseData = await response.json();
      return this.transformBackendNotification(responseData.data.notification);
    } catch (error) {
      console.error('Create notification error:', error);
      throw error;
    }
  }

  private static transformBackendNotification(backendNotification: any): AdminNotification {
    return {
      id: backendNotification.id.toString(),
      type: backendNotification.type,
      title: backendNotification.title,
      message: backendNotification.message,
      data: backendNotification.data,
      read: backendNotification.read || false,
      readAt: backendNotification.readAt,
      createdAt: backendNotification.createdAt,
      priority: backendNotification.priority || 'medium',
      category: backendNotification.category || 'system'
    };
  }

  private static getFallbackNotifications(): AdminNotification[] {
    return [
      {
        id: 'notif-1',
        type: 'order_new',
        title: 'سفارش جدید',
        message: 'سفارش جدیدی با شماره #1234 ثبت شده است',
        data: { orderId: '1234' },
        read: false,
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        priority: 'high',
        category: 'order'
      },
      {
        id: 'notif-2',
        type: 'product_low_stock',
        title: 'موجودی کم',
        message: 'موجودی محصول "سرم هیالورونیک اسید" کم شده است',
        data: { productId: '1' },
        read: false,
        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        priority: 'medium',
        category: 'inventory'
      }
    ];
  }
}






export const useAdminNotifications = () => {
  const [notifications, setNotifications] = useState<AdminNotification[]>([]);
  const [auditLogs, setAuditLogs] = useState<AuditLogEntry[]>([]);
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<NotificationFilters>({});
  const [auditFilters, setAuditFilters] = useState<AuditLogFilters>({});

  const fetchNotifications = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const notificationsData = await AdminNotificationApiService.getNotifications(filters);
      setNotifications(notificationsData);

      // TODO: Implement audit logs and settings API
      setAuditLogs([]);
      setNotificationSettings(null);

    } catch (err) {
      setError('خطا در بارگذاری اعلان‌ها');
      console.error('Notifications fetch error:', err);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      await AdminNotificationApiService.markAsRead(notificationId);

      setNotifications(prev =>
        prev.map(notif =>
          notif.id === notificationId
            ? { ...notif, read: true, readAt: new Date().toISOString() }
            : notif
        )
      );

      return { success: true };
    } catch (err) {
      console.error('Mark as read error:', err);
      toast.error('خطا در علامت‌گذاری به عنوان خوانده شده');
      return { success: false, error: 'خطا در علامت‌گذاری به عنوان خوانده شده' };
    }
  }, []);

  const markAllAsRead = useCallback(async () => {
    try {
      await AdminNotificationApiService.markAllAsRead();

      const now = new Date().toISOString();
      setNotifications(prev =>
        prev.map(notif => ({ ...notif, read: true, readAt: now }))
      );

      toast.success('همه اعلان‌ها به عنوان خوانده شده علامت‌گذاری شدند');
      return { success: true };
    } catch (err) {
      console.error('Mark all as read error:', err);
      toast.error('خطا در علامت‌گذاری همه اعلان‌ها');
      return { success: false, error: 'خطا در علامت‌گذاری همه اعلان‌ها' };
    }
  }, []);

  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      await AdminNotificationApiService.deleteNotification(notificationId);

      setNotifications(prev => prev.filter(notif => notif.id !== notificationId));

      toast.success('اعلان حذف شد');
      return { success: true };
    } catch (err) {
      console.error('Delete notification error:', err);
      toast.error('خطا در حذف اعلان');
      return { success: false, error: 'خطا در حذف اعلان' };
    }
  }, []);

  const updateNotificationSettings = useCallback(async (settings: Partial<NotificationSettings>) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      setNotificationSettings(prev => prev ? { ...prev, ...settings } : null);

      return { success: true };
    } catch (err) {
      console.error('Update settings error:', err);
      return { success: false, error: 'خطا در ذخیره تنظیمات' };
    }
  }, []);

  const createNotification = useCallback(async (
    type: NotificationType,
    title: string,
    message: string,
    data?: any
  ) => {
    try {
      const newNotification = await AdminNotificationApiService.createNotification(type, title, message, data);
      setNotifications(prev => [newNotification, ...prev]);

      toast.success('اعلان جدید ایجاد شد');
      return { success: true, notification: newNotification };
    } catch (err) {
      toast.error('خطا در ایجاد اعلان');
      return { success: false, error: 'خطا در ایجاد اعلان' };
    }
  }, []);

  // Test function to create a sample notification
  const createTestNotification = useCallback(async () => {
    return await createNotification(
      'order_new',
      'سفارش تست جدید',
      `سفارش تست #${Math.floor(Math.random() * 10000)} ثبت شد`
    );
  }, [createNotification]);

  // Filter notifications based on current filters
  const filteredNotifications = notifications.filter(notif => {
    if (filters.category && !filters.category.includes(notif.category)) return false;
    if (filters.type && !filters.type.includes(notif.type)) return false;
    if (filters.priority && !filters.priority.includes(notif.priority)) return false;
    if (filters.read !== undefined && notif.read !== filters.read) return false;

    if (filters.dateRange) {
      const notifDate = new Date(notif.createdAt);
      const startDate = new Date(filters.dateRange.start);
      const endDate = new Date(filters.dateRange.end);
      if (notifDate < startDate || notifDate > endDate) return false;
    }

    return true;
  });

  // Filter audit logs based on current filters
  const filteredAuditLogs = auditLogs.filter(log => {
    if (auditFilters.userId && log.userId !== auditFilters.userId) return false;
    if (auditFilters.action && !auditFilters.action.includes(log.action)) return false;
    if (auditFilters.resource && !auditFilters.resource.includes(log.resource)) return false;
    if (auditFilters.success !== undefined && log.success !== auditFilters.success) return false;
    if (auditFilters.ipAddress && log.ipAddress !== auditFilters.ipAddress) return false;
    
    if (auditFilters.dateRange) {
      const logDate = new Date(log.timestamp);
      const startDate = new Date(auditFilters.dateRange.start);
      const endDate = new Date(auditFilters.dateRange.end);
      if (logDate < startDate || logDate > endDate) return false;
    }
    
    return true;
  });

  const unreadCount = notifications.filter(notif => !notif.read).length;

  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  return {
    notifications: filteredNotifications,
    auditLogs: filteredAuditLogs,
    notificationSettings,
    loading,
    error,
    filters,
    setFilters,
    auditFilters,
    setAuditFilters,
    unreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    updateNotificationSettings,
    createNotification,
    createTestNotification,
    refetch: fetchNotifications
  };
};
